"use client"

import React, { useRef, useState } from "react"
import html2canvas from "html2canvas"
import jsPD<PERSON> from "jspdf"
import { Download, Loader2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import ClinicPatientInsights from "@/components/summary/ClinicPatientInsights"
import DateFilter from "@/components/summary/DateFilter"
import FinancialPNL from "@/components/summary/FinancialPNL"
import IVFInsights from "@/components/summary/IVFInsights"
import OperationsHR from "@/components/summary/OperationsHR"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Summary = () => {
  const contentRef = useRef<HTMLDivElement>(null)

  return (
    <>
      <Breadcrumb links={[{ label: "Summary" }]} />

      <div className="flex justify-between gap-4">
        <DateFilter />
        <ExportButton contentRef={contentRef} />
      </div>

      <div ref={contentRef} className="flex flex-col gap-4">
        <FinancialPNL />

        <IVFInsights />

        <ClinicPatientInsights />

        <OperationsHR />
      </div>
    </>
  )
}

export default Summary

const ExportButton = ({
  contentRef,
}: {
  contentRef: React.RefObject<HTMLDivElement | null>
}) => {
  const [isExporting, setIsExporting] = useState(false)

  const handleExport = async () => {
    if (!contentRef.current) return

    setIsExporting(true)
    try {
      // Add temporary style to override problematic colors
      const tempStyle = document.createElement("style")
      tempStyle.id = "pdf-export-override"
      tempStyle.textContent = `
        #pdf-export-content * {
          color: rgb(0 0 0) !important;
          background-color: rgb(255 255 255) !important;
          border-color: rgb(229 231 235) !important;
        }
        #pdf-export-content .text-primary { color: rgb(59 130 246) !important; }
        #pdf-export-content .bg-primary { background-color: rgb(59 130 246) !important; }
        #pdf-export-content .bg-primary\\/10 { background-color: rgba(59, 130, 246, 0.1) !important; }
        #pdf-export-content .text-green-600 { color: rgb(22 163 74) !important; }
        #pdf-export-content .text-muted-foreground { color: rgb(107 114 128) !important; }
        #pdf-export-content .bg-card { background-color: rgb(255 255 255) !important; }
        #pdf-export-content .border { border-color: rgb(229 231 235) !important; }
        #pdf-export-content [style*="var(--chart-1)"] { color: rgb(59 130 246) !important; }
        #pdf-export-content [style*="var(--chart-2)"] { color: rgb(16 185 129) !important; }
        #pdf-export-content [style*="var(--chart-3)"] { color: rgb(245 158 11) !important; }
        #pdf-export-content [style*="var(--chart-4)"] { color: rgb(239 68 68) !important; }
        #pdf-export-content [style*="var(--chart-5)"] { color: rgb(139 92 246) !important; }
      `
      document.head.appendChild(tempStyle)

      // Add temporary ID to content for styling
      const originalId = contentRef.current.id
      contentRef.current.id = "pdf-export-content"

      // Wait a bit for styles to apply
      await new Promise((resolve) => setTimeout(resolve, 100))

      // Create canvas from the content
      const canvas = await html2canvas(contentRef.current, {
        scale: 2, // Higher quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        logging: false,
        width: contentRef.current.scrollWidth,
        height: contentRef.current.scrollHeight,
      })

      // Cleanup temporary styles
      document.head.removeChild(tempStyle)
      contentRef.current.id = originalId

      // Calculate PDF dimensions
      const imgWidth = 210 // A4 width in mm
      const pageHeight = 295 // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight

      // Create PDF
      const pdf = new jsPDF("p", "mm", "a4")
      let position = 0

      // Add first page
      pdf.addImage(
        canvas.toDataURL("image/png"),
        "PNG",
        0,
        position,
        imgWidth,
        imgHeight
      )
      heightLeft -= pageHeight

      // Add additional pages if content is longer than one page
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(
          canvas.toDataURL("image/png"),
          "PNG",
          0,
          position,
          imgWidth,
          imgHeight
        )
        heightLeft -= pageHeight
      }

      // Download the PDF
      const currentDate = new Date().toISOString().split("T")[0]
      pdf.save(`summary-report-${currentDate}.pdf`)
    } catch (error) {
      console.error("Error generating PDF:", error)
      // You could add a toast notification here for better UX
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <Button onClick={handleExport} disabled={isExporting}>
      {isExporting ? <Loader2 className="animate-spin" /> : <Download />}
      Export
    </Button>
  )
}
