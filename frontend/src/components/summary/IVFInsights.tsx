"use client"

import React from "react"
import { Activity } from "lucide-react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { formatCurrency } from "@/lib/number"
import { Card } from "@/components/ui/card"
import {
  CardContainer,
  CardLabel,
  CardTitleWithIcon,
  Title,
} from "@/components/summary/Components"
import { CommentPopover } from "@/components/summary/FinancialPNL"

type CycleData = {
  month: string
  inflow: number
  outflow: number
  net: number
}

const epuCycleData: CycleData[] = [
  { month: "Jul", inflow: 9000, outflow: 4500, net: 4500 },
  { month: "Aug", inflow: 9500, outflow: 4200, net: 5300 },
  { month: "Sep", inflow: 9200, outflow: 4800, net: 4400 },
  { month: "Oct", inflow: 10500, outflow: 5200, net: 5300 },
  { month: "Nov", inflow: 8800, outflow: 4600, net: 4200 },
  { month: "Dec", inflow: 9300, outflow: 4400, net: 4900 },
  { month: "Jan", inflow: 9700, outflow: 4700, net: 5000 },
  { month: "Feb", inflow: 9100, outflow: 4300, net: 4800 },
  { month: "Mar", inflow: 9600, outflow: 4500, net: 5100 },
  { month: "Apr", inflow: 8900, outflow: 4400, net: 4500 },
  { month: "May", inflow: 9400, outflow: 4600, net: 4800 },
  { month: "Jun", inflow: 9000, outflow: 4200, net: 4800 },
]

const fetCycleData: CycleData[] = [
  { month: "Jul", inflow: 8500, outflow: 4200, net: 4300 },
  { month: "Aug", inflow: 9000, outflow: 4000, net: 5000 },
  { month: "Sep", inflow: 8800, outflow: 4500, net: 4300 },
  { month: "Oct", inflow: 10000, outflow: 4900, net: 5100 },
  { month: "Nov", inflow: 8300, outflow: 4300, net: 4000 },
  { month: "Dec", inflow: 8800, outflow: 4100, net: 4700 },
  { month: "Jan", inflow: 9200, outflow: 4400, net: 4800 },
  { month: "Feb", inflow: 8600, outflow: 4000, net: 4600 },
  { month: "Mar", inflow: 9100, outflow: 4200, net: 4900 },
  { month: "Apr", inflow: 8400, outflow: 4100, net: 4300 },
  { month: "May", inflow: 8900, outflow: 4300, net: 4600 },
  { month: "Jun", inflow: 8500, outflow: 3900, net: 4600 },
]

const IVFInsights = () => (
  <>
    <Title title="IVF Insights" />

    <div className="grid gap-4 lg:grid-cols-2">
      <IVFCycleCard title="EPU Cycle" data={epuCycleData} />
      <IVFCycleCard title="FET Cycle" data={fetCycleData} />
    </div>
  </>
)

export default IVFInsights

const IVFCycleCard = ({
  title,
  data,
}: {
  title: string
  data: CycleData[]
}) => (
  <Card>
    <CardTitleWithIcon icon={Activity} title={title}>
      <CommentPopover />
    </CardTitleWithIcon>

    <CardContainer>
      <ResponsiveContainer width="100%" height={200}>
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />

          <XAxis dataKey="month" tick={{ fontSize: 12 }} />
          <YAxis
            width={40}
            tickFormatter={(v) => formatCurrency(v, 0)}
            tick={{ fontSize: 12 }}
          />

          <Tooltip formatter={(value: number) => `$${formatCurrency(value)}`} />

          <Legend verticalAlign="bottom" wrapperStyle={{ fontSize: 10 }} />

          <Bar
            dataKey="inflow"
            name="Operating Cash Inflow"
            fill="var(--chart-1)"
          />

          <Bar
            dataKey="outflow"
            name="Operating Cash Outflow"
            fill="var(--chart-3)"
          />

          <Line
            type="monotone"
            dataKey="net"
            name="Net Operating Cash Flow"
            stroke="var(--chart-5)"
            strokeWidth={2}
          />
        </ComposedChart>
      </ResponsiveContainer>

      <div className="flex flex-col gap-1">
        <CardLabel label="Country Breakdown" />

        <p className="flex items-center gap-1.5">
          <span>🇦🇺</span>
          <span className="text-sm">CFC: $50M</span>
        </p>

        <p className="flex items-center gap-1.5">
          <span>🇸🇬</span>
          <span className="text-sm">SMG: $50M</span>
        </p>
      </div>

      <div className="flex flex-col gap-1">
        <CardLabel label="Analysis" />

        <p className="text-sm">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua.
        </p>
      </div>
    </CardContainer>
  </Card>
)
