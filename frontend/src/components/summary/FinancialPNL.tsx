"use client"

import React, { useState } from "react"
import {
  BarChart3,
  Building2,
  CircleDollarSign,
  Coins,
  DollarSign,
  LucideIcon,
  MessageSquare,
  PiggyBank,
  Receipt,
  TrendingUp,
  Wallet,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  CardContainer,
  CardLabel,
  CardTitleWithIcon,
  Title,
} from "@/components/summary/Components"

const CARDS = [
  {
    icon: DollarSign,
    title: "Revenue",
  },
  {
    icon: PiggyBank,
    title: "Gross Profit",
  },
  {
    icon: Wallet,
    title: "Net Profit",
  },
  {
    icon: Receipt,
    title: "Operating Expenses",
  },
  {
    icon: Coins,
    title: "Cash Balance",
  },
  {
    icon: Building2,
    title: "Long Term Bank Debt",
  },
  {
    icon: CircleDollarSign,
    title: "Net Debt",
  },
  {
    icon: BarChart3,
    title: "CAPEX",
  },
]

const FinancialPNL = () => (
  <>
    <Title title="Financial P&L" />

    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {CARDS.map((card, index) => (
        <PNLCard key={index} icon={card.icon} title={card.title} />
      ))}
    </div>
  </>
)

export default FinancialPNL

export const CommentPopover = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [comment, setComment] = useState<string>("")

  const handleSubmit = () => {
    if (comment.trim()) {
      // Handle comment submission here
      console.log("Comment submitted:", comment)
      setComment("")
      setIsOpen(false)
    }
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <Tooltip>
        <PopoverTrigger asChild>
          <TooltipTrigger asChild>
            <Button variant="outline" size="icon" className="ml-auto size-8">
              <MessageSquare />
            </Button>
          </TooltipTrigger>
        </PopoverTrigger>

        <TooltipContent side="bottom">Add Comment</TooltipContent>
      </Tooltip>

      <PopoverContent
        className="w-80"
        align="end"
        onCloseAutoFocus={(e) => e.preventDefault()}
      >
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Add Comment</h4>
          <Textarea
            placeholder="Enter your comment..."
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            className="min-h-[80px] resize-none"
          />
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsOpen(false)}
            >
              Cancel
            </Button>
            <Button size="sm" onClick={handleSubmit} disabled={!comment.trim()}>
              Submit
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}

const PNLCard = ({
  icon,
  title,
}: {
  icon: LucideIcon | React.ElementType
  title: string
}) => (
  <Card>
    <CardTitleWithIcon icon={icon} title={title}>
      <CommentPopover />
    </CardTitleWithIcon>

    <CardContainer>
      <div className="-mb-1 flex items-baseline justify-between gap-1.5">
        <p className="text-2xl font-bold">$100M</p>
        <p className="inline-flex items-center gap-1.5 text-green-600">
          <TrendingUp className="size-3.5" />
          5.4%
        </p>
      </div>

      <div className="flex flex-col gap-1">
        <div className="flex items-center justify-between gap-2 text-sm">
          <p className="text-muted-foreground">Target: $180</p>
          <p className="font-medium text-green-600">102.8% of target</p>
        </div>

        <Progress value={100} className="[&>div]:bg-green-600" />
      </div>

      <div className="flex flex-col gap-1">
        <CardLabel label="Country Breakdown" />

        <p className="flex items-center gap-1.5">
          <span>🇦🇺</span>
          <span className="text-sm">CFC: $50M</span>
          <span className="ml-auto inline-flex items-center gap-1.5 text-sm text-green-600">
            <TrendingUp className="size-3" />
            5.4%
          </span>
        </p>

        <p className="flex items-center gap-1.5">
          <span>🇸🇬</span>
          <span className="text-sm">SMG: $50M</span>
          <span className="ml-auto inline-flex items-center gap-1.5 text-sm text-green-600">
            <TrendingUp className="size-3" />
            5.4%
          </span>
        </p>
      </div>

      <div className="flex flex-col gap-1">
        <CardLabel label="Analysis" />

        <p className="text-sm">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua.
        </p>
      </div>
    </CardContainer>
  </Card>
)
